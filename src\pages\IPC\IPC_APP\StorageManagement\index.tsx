import { useState, useEffect } from "react";
import { Image, List, Toast } from "antd-mobile";
import {
  useHistory,
  useRouteMatch,
  Route,
  useLocation,
} from "react-router-dom";
import { StorageContext } from "./Context/storageContext";
import styles from "./index.module.scss";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import cameraTest from "@/Resources/camMgmtImg/camera-test.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import NavigatorBar from "@/components/NavBar";
import { useTheme } from "@/utils/themeDetector";
import { CameraInfo, listRecordCamera } from "@/api/ipc";
import { useRequest } from "ahooks";

const StorageManagement = (props: { children?: React.ReactNode }) => {
  const history = useHistory();
  const location = useLocation();
  const { path } = useRouteMatch();
  const { isDarkMode } = useTheme();

  const [cameras, setCameras] = useState<CameraInfo[]>([]);

  const { run: refreshCameraList } = useRequest(() => listRecordCamera({ did: [] }), {
    onSuccess: (res) => {
      if(res && (res.code === 5000 || res.code === 1700)){
        Toast.show(res?.result);
        return;
      }
      const cameraList = res.data?.camera || [];
      setCameras(cameraList);
    },
    onError: (err) => {
      console.log('err: ', err);
    },
  });

  // 当从设备详情页返回时刷新数据
  useEffect(() => {
    // 当路径变为存储管理主页面时，刷新摄像机列表
    if (location.pathname === path) {
      refreshCameraList();
    }
  }, [location.pathname, path, refreshCameraList]);

  // 初始化阈值和检测事件状态
  const [threshold, setThreshold] = useState(90);
  const [detectEvents, setDetectEvents] = useState({
    motionDetect: true,
    humanDetect: true,
    fireDetect: true,
    petDetect: true,
    soundDetect: true,
  });

  return (
    <StorageContext.Provider
      value={{
        threshold,
        setThreshold,
        detectEvents,
        setDetectEvents,
      }}
    >
      <div className={styles.container}>
        {props.children}
        <Route exact path={path}>
          <NavigatorBar backIcon={isDarkMode ? arrowLeftDark : arrowLeft} />
          <div className={styles.title}>存储管理</div>
          <List className={styles.cameraList}>
            {cameras.map((camera) => (
              <List.Item
                key={camera.did}
                prefix={<Image src={cameraTest} className={styles.cameraImg} />}
                title={camera.name}
                arrowIcon
                onClick={() => {
                  history.push({ pathname: `${path}/detail/${camera.did}`, state: { camera } });
                }}
              />
            ))}
          </List>
        </Route>
      </div>
    </StorageContext.Provider>
  );
};

export default StorageManagement;
