import React, { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, Toast } from "antd-mobile";
import styles from "./index.module.scss";
import { useHistory, useLocation } from "react-router-dom";
import { useRequest } from "ahooks";
import {
  getPoolInfo,
  listDirectory,
  StoragePool,
} from "@/api/fatWall";
import { PreloadImage } from "@/components/Image";
import fileIcon from "@/Resources/nasDiskImg/file-icon.png";
import CreateNasFolder from "../components/CreateNasFolder";
import { WebDavInfo } from "@/utils/DeviceType";
import { useTheme } from "@/utils/themeDetector";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import createIcon from "@/Resources/nasDiskImg/create.png";
import createIconDark from "@/Resources/nasDiskImg/create-dark.png";
// 内部使用的文件项接口
interface FileItem {
  id: string;
  name: string;
  path: string;
  type: "folder" | "file";
  time: string;
  size?: number;
  isdir?: boolean;
  isLiked?: boolean;
  isDirectory?: boolean;
  dataDir?: string; // 存储池的原始data_dir
}

// 面包屑导航项
interface BreadcrumbItem {
  id: string;
  name: string;
  path: string;
}

// 接收的位置状态
interface LocationState {
  from?: string;
  isVip?: boolean;
  isEditing?: boolean; // 添加编辑状态
  selectedIds?: number[]; // 添加已选中的文件ID列表
  sourceType?: "fileList" | "autoDownload"; // 添加来源类型，区分普通下载和自动下载
  selectedFolders?: string[]; // 添加已选中的文件夹ID列表（用于自动下载）
}

const Downlocation: React.FC = () => {
  const { isDarkMode } = useTheme();
  const history = useHistory();
  const location = useLocation<LocationState>();
  const { from, isVip, isEditing, selectedIds, sourceType, selectedFolders } =
    location.state || {};

  // 文件列表
  const [fileList, setFileList] = useState<FileItem[]>([]);

  // 选中的文件夹路径
  const [selectedFolder, setSelectedFolder] = useState<string>("");

  // 当前路径面包屑
  const [breadcrumbPath, setBreadcrumbPath] = useState<BreadcrumbItem[]>([]);

  // 加载状态
  const [loading, setLoading] = useState<boolean>(false);

  // 新建文件夹弹窗状态
  const [showCreateFolderModal, setShowCreateFolderModal] =
    useState<boolean>(false);

  // 当前目录路径（用于创建文件夹）
  const [currentDirectoryPath, setCurrentDirectoryPath] = useState<string>("");

  // 当前存储池名称
  const [, setCurrentPoolName] = useState<string>("百度网盘");

  // 存储池列表
  const [storagePools, setStoragePools] = useState<StoragePool[]>([]);

  // webDAV配置信息
  const [webDAVConfig, setWebDAVConfig] = useState<{
    alias_root: string;
  } | null>(null);

  const [webDavConfig, setWebDavConfig] = useState<WebDavInfo>();

  // 当前调用 list_directory 时传递的 path.parent 值
  const [currentPathParent, setCurrentPathParent] = useState<string>("");

  // 获取存储池信息
  const { run: fetchPoolInfo, loading: poolLoading } = useRequest(getPoolInfo, {
    manual: true,
    onSuccess: (response) => {
      if (response.code === 0 && response.data) {
        setStoragePools(response.data.internal_pool);
        // 获取WebDAV配置
        if (response.data.webDAV) setWebDavConfig(response.data.webDAV);

        // 保存webDAV配置
        if (response.data.webDAV) {
          setWebDAVConfig({
            alias_root: response.data.webDAV.alias_root,
          });
        }

        // 获取第一个存储池的顶层目录作为顶层文件夹
        if (response.data.internal_pool.length > 0) {
          const firstPool = response.data.internal_pool[0];

          // 设置当前存储池名称
          setCurrentPoolName(firstPool.name);

          // 设置初始面包屑
          const initialBreadcrumb: BreadcrumbItem = {
            id: `pool_0`,
            name: firstPool.name,
            path: firstPool.data_dir,
          };
          setBreadcrumbPath([initialBreadcrumb]);

          let pathParent = firstPool.data_dir;
          setCurrentDirectoryPath(pathParent);

          if (response.data.webDAV?.alias_root) {
            const dataDir = firstPool.data_dir.endsWith("/")
              ? firstPool.data_dir.slice(0, -1)
              : firstPool.data_dir;
            const aliasRoot = response.data.webDAV.alias_root;
            pathParent = aliasRoot + dataDir;
          }

          // 保存当前的 path.parent 值
          setCurrentPathParent(pathParent);

          // 获取顶层目录
          fetchDirectoryList({
            path: {
              parent: pathParent,
              recursion: false,
            },
          });
        }
      }
    },
    onError: (error) => {
      console.error("获取存储池信息失败：", error);
      Toast.show({
        content: "获取存储池信息失败，请重试",
        position: "bottom",
        duration: 2000,
      });
      setFileList([]);
    },
  });

  // 获取目录列表
  const { run: fetchDirectoryList, loading: directoryLoading } = useRequest(
    listDirectory,
    {
      manual: true,
      onSuccess: (response) => {
        if (response.code === 0 && response.data) {
          const files: FileItem[] = response.data.files
            .filter((file) => file.xattr.directory)
            .map((file, index) => ({
              id: `file_${index}`,
              name: file.name,
              type: "folder" as const,
              time: new Date(parseInt(file.modified_time))
                .toLocaleDateString("zh-CN", {
                  year: "numeric",
                  month: "2-digit",
                  day: "2-digit",
                  hour: "2-digit",
                  minute: "2-digit",
                })
                .replace(/\//g, "/")
                .replace(/,/g, ""),
              path: `${file.parent}/${file.name}`,
              isDirectory: file.xattr.directory,
              isLiked: file.xattr.favorite,
            }));
          setFileList(files);
          setLoading(false);
        }
      },
      onError: (error) => {
        console.error("获取目录列表失败：", error);
        Toast.show({
          content: "获取目录列表失败，请重试",
          position: "bottom",
          duration: 2000,
        });
        setFileList([]);
        setLoading(false);
      },
    }
  );

  // 初始化时获取存储池信息
  useEffect(() => {
    setLoading(true);
    fetchPoolInfo({});
  }, [fetchPoolInfo]);

  // 处理文件夹点击 - 导航到子文件夹
  const handleFolderClick = useCallback(
    (folder: FileItem) => {
      // 设置选中的文件夹
      setSelectedFolder(folder.path);

      // 导航到子文件夹
      setLoading(true);

      // 更新面包屑
      const newBreadcrumb: BreadcrumbItem = {
        id: folder.id,
        name: folder.name,
        path: folder.path,
      };
      setBreadcrumbPath((prev) => [...prev, newBreadcrumb]);

      // 更新当前路径
      setCurrentDirectoryPath(folder.path);
      setCurrentPathParent(folder.path);

      // 获取子文件夹内容
      fetchDirectoryList({
        path: {
          parent: folder.path,
          recursion: false,
        },
      });
    },
    [fetchDirectoryList]
  );

  // 处理面包屑点击 - 导航到指定目录
  const handleBreadcrumbClick = useCallback(
    (index: number) => {
      // 如果点击的是当前位置，不做任何操作
      if (index === breadcrumbPath.length - 1) {
        return;
      }

      setLoading(true);

      // 截取到点击的位置
      const newPath = breadcrumbPath.slice(0, index + 1);
      setBreadcrumbPath(newPath);

      // 获取指定路径的目录列表
      const targetItem = newPath[newPath.length - 1];

      // 如果是第一层（存储池层级），需要构造特殊的路径
      if (newPath.length === 1) {
        // 找到对应的存储池数据
        const poolData = storagePools.find(
          (pool) => pool.name === targetItem.name
        );
        if (poolData && webDAVConfig?.alias_root) {
          // 构造正确的path.parent：data_dir + alias_root，避免双斜杠
          const dataDir = poolData.data_dir.endsWith("/")
            ? poolData.data_dir.slice(0, -1)
            : poolData.data_dir;
          const aliasRoot = webDAVConfig.alias_root;
          const pathParent = aliasRoot + dataDir;

          // 保存当前的 path.parent 值
          setCurrentPathParent(pathParent);
          setCurrentDirectoryPath(poolData.data_dir);

          fetchDirectoryList({
            path: {
              parent: pathParent,
              recursion: false,
            },
          });
        }
      } else {
        // 普通的文件夹层级
        const targetPath = targetItem.path;

        // 保存当前的 path.parent 值
        setCurrentPathParent(targetPath);
        setCurrentDirectoryPath(targetPath);

        fetchDirectoryList({
          path: {
            parent: targetPath,
            recursion: false,
          },
        });
      }

      // 清除选中状态
      setSelectedFolder("");
    },
    [breadcrumbPath, fetchDirectoryList, storagePools, webDAVConfig]
  );

  // 处理返回
  const handleBack = () => {
    // 如果有来源页面，则返回到该页面
    if (from) {
      // 不保存任何路径信息，仅返回并保持在网盘文件标签页
      history.push({
        pathname: from,
        state: {
          isVip: isVip,
          isEditing: isEditing,
          selectedIds: selectedIds,
          selectedFolders: selectedFolders, // 保持已选中的文件夹（用于自动下载）
          activeTab: "diskfiles", // 确保返回时保持在网盘文件标签页
          sourceType: sourceType, // 保持来源类型
        },
      });
    } else {
      history.goBack();
    }
  };

  // 处理新建文件夹
  const handleCreateFolder = () => {
    setShowCreateFolderModal(true);
  };

  // 处理创建文件夹成功
  const handleCreateFolderSuccess = () => {
    setShowCreateFolderModal(false);

    // 刷新当前目录
    setLoading(true);
    fetchDirectoryList({
      path: {
        parent: currentPathParent,
        recursion: false,
      },
    });
  };

  // 处理创建文件夹取消
  const handleCreateFolderCancel = () => {
    setShowCreateFolderModal(false);
  };

  // 处理确定按钮
  const handleConfirm = () => {
    if (breadcrumbPath.length === 0) {
      Toast.show({
        content: "请先选择一个存储池",
        position: "bottom",
        duration: 2000,
      });
      return;
    }

    // 使用当前面包屑导航的路径
    const currentLocation = currentPathParent;
    // 获取面包屑显示路径
    const displayPath = breadcrumbPath.map((item) => item.name).join("/");

    Toast.show({
      content: `已设置${
        sourceType === "autoDownload" ? "自动" : ""
      }下载位置：${displayPath}`,
      position: "bottom",
      duration: 2000,
    });

    // 返回上一页，并传递选择的路径信息和编辑状态
    if (from) {
      history.push({
        pathname: from,
        state: {
          downloadPath: currentLocation,
          downloadDisplayPath: displayPath,
          isVip: isVip,
          isEditing: sourceType !== "autoDownload" ? true : undefined, // 普通下载时保持编辑状态
          selectedIds: selectedIds, // 保持已选中的文件
          selectedFolders: selectedFolders, // 保持已选中的文件夹（用于自动下载）
          activeTab: "diskfiles", // 确保返回时保持在网盘文件标签页
          sourceType: sourceType, // 保持来源类型
        },
      });
    } else {
      history.goBack();
    }
  };

  return (
    <div className={styles.uploadBDContainer}>
      <div className={styles.fixedHeader}>
        {/* 头部导航栏 */}
        <div className={styles.headerNavigation}>
          <div className={styles.leftSection}>
            <PreloadImage
              src={isDarkMode ? arrowLeftDark : arrowLeft}
              alt="返回"
              className={styles.backButton}
              onClick={handleBack}
            />
            <div className={styles.title}>
              {sourceType === "autoDownload" ? "更改自动下载位置" : "更改下载位置"}
            </div>
          </div>
          <div className={styles.rightSection}>
            <PreloadImage
              src={isDarkMode ? createIconDark : createIcon}
              alt="新建文件夹"
              className={styles.createButton}
              onClick={handleCreateFolder}
            />
          </div>
        </div>

        {/* 面包屑导航 */}
        <div className={styles.breadcrumb}>
          {breadcrumbPath.map((item, index) => (
            <React.Fragment key={index}>
              <span
                className={`${styles.breadcrumbItem} ${
                  index === breadcrumbPath.length - 1 ? styles.active : ""
                }`}
                onClick={() => handleBreadcrumbClick(index)}
              >
                {item.name}
              </span>
              {index < breadcrumbPath.length - 1 && (
                <span className={styles.breadcrumbSeparator}>&gt;</span>
              )}
            </React.Fragment>
          ))}
        </div>
      </div>

      <div className={styles.scrollableContent}>
        {/* 文件列表 */}
        <div className={styles.fileList}>
          {!loading &&
            !poolLoading &&
            !directoryLoading &&
            fileList.map((folder) => (
              <div
                key={folder.id}
                className={`${styles.fileItem} ${
                  selectedFolder === folder.path ? styles.selected : ""
                }`}
                onClick={() => handleFolderClick(folder)}
              >
                <div className={styles.fileIcon}>
                  <PreloadImage
                    src={fileIcon}
                    alt=""
                    style={{ width: 40, height: 40 }}
                  />
                </div>
                <div className={styles.fileInfo}>
                  <div className={styles.fileName}>{folder.name}</div>
                  <div className={styles.fileDetails}>{folder.time}</div>
                </div>
              </div>
            ))}

          {!loading &&
            !poolLoading &&
            !directoryLoading &&
            fileList.length === 0 && (
              <div className={styles.emptyState}>
                <span>该目录下没有文件夹</span>
              </div>
            )}
        </div>
      </div>

      {/* 底部按钮 */}
      <div className={styles.footer}>
        <div className={styles.newFolderButton} onClick={handleCreateFolder}>
          <div className={styles.folderIconContainer}>
            <PreloadImage
              src={isDarkMode ? createIconDark : createIcon}
              alt="新建文件夹"
              className={styles.folderIcon}
            />
          </div>
          <span className={styles.newFolderText}>新建文件夹</span>
        </div>
        <Button className={styles.confirmButton} onClick={handleConfirm}>
          更改到当前位置
        </Button>
      </div>

      {/* 新建文件夹弹窗 */}
      <CreateNasFolder
        visible={showCreateFolderModal}
        onCancel={handleCreateFolderCancel}
        onSuccess={handleCreateFolderSuccess}
        currentPath={currentDirectoryPath}
        webDavConfig={webDavConfig}
      />
    </div>
  );
};

export default Downlocation;
