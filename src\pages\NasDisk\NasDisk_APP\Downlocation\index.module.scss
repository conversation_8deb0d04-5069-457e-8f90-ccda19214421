.uploadBDContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--background-color);
  overflow: hidden;
}

.fixedHeader {
  flex-shrink: 0;
  background-color: var(--background-color);
}

// 头部导航栏样式
.headerNavigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  position: relative;
  border-radius: 24px 24px 0 0;

  .leftSection {
    display: flex;
    align-items: center;

    .backButton {
      width: 40px;
      height: 40px;
      cursor: pointer;
    }
  }

  .centerSection {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

    .title {
      font-size: 18px;
      color: var(--text-color);
      font-weight: 500;
      margin: 0;
      white-space: nowrap;
    }
  }

  .rightSection {
    .createButton {
      width: 40px;
      height: 40px;
      cursor: pointer;
    }
  }
}

.breadcrumb {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: var(--background-color);
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 8px;

  &::-webkit-scrollbar {
    display: none;
  }

  .breadcrumbItem {
    color: rgba(255, 178, 29, 1);
    background-color: #fff4dd;
    padding: 5px 10px;
    border-radius: 10px;
    font-size: 14px;
    cursor: pointer;
    flex-shrink: 0;

    &.active {
      background-color: rgba(255, 178, 29, 0.2);
      color: rgba(255, 178, 29, 1);
      font-weight: 500;
    }

    &:hover {
      opacity: 0.8;
    }
  }

  .breadcrumbSeparator {
    margin: 0 4px;
    color: var(--subtitle-text-color);
    font-size: 12px;
    flex-shrink: 0;
  }
}

.scrollableContent {
  flex: 1;
  overflow-y: auto;
  // height:100%;
  padding-bottom: 80px;
}

// 加载状态样式
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 16px;
  color: var(--subtitle-text-color);

  span {
    margin-top: 12px;
    font-size: 14px;
  }
}

.fileList {
  padding: 0 16px;

  .fileItem {
    display: flex;
    align-items: center;
    padding: 12px 0;
    cursor: pointer;
    border-radius: 8px;
    margin-bottom: 4px;
    transition: background-color 0.2s ease;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.02);
    }

    &.selected {
      background-color: rgba(50, 186, 192, 0.1);
      border: 1px solid rgba(50, 186, 192, 0.3);
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }

    .fileIcon {
      width: 40px;
      height: 40px;
      margin-right: 12px;
      margin-left: 8px;
      display: flex;
      align-items: center;
      justify-content: center;

      .folderIcon {
        width: 100%;
        height: 100%;
        background-color: #ffc14f;
        border-radius: 4px;
        mask-size: cover;
        -webkit-mask-size: cover;
        mask-repeat: no-repeat;
        -webkit-mask-repeat: no-repeat;
        mask-position: center;
        -webkit-mask-position: center;
      }
    }

    .fileInfo {
      flex: 1;
      overflow: hidden;
      padding-right: 8px;

      .fileName {
        display: flex;
        align-items: center;
        font-size: 16px;
        color: var(--text-color);
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 400;
      }

      .fileDetails {
        font-size: 12px;
        color: var(--subtitle-text-color);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .emptyState {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 16px;
    color: var(--subtitle-text-color);
    font-size: 14px;
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 28px;
  background-color: var(--background-color);
  z-index: 100;

  .confirmButton {
    width: 100%;
    height: 48px;
    border-radius: 24px;
    background-color: var(--primary-color);
    border: none;
    font-size: 16px;
    font-weight: 500;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    &:active {
      background-color: var(--primary-color);
    }

    &:disabled {
      background-color: var(--primary-color);
      opacity: 0.3;
      cursor: not-allowed;
    }
  }
}
