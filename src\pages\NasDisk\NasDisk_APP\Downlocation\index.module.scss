.uploadBDContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--background-color);
  border-radius: 24px 24px 0 0;
  overflow: hidden;
}

.fixedHeader {
  flex-shrink: 0;
  background-color: var(--background-color);
}

// 头部导航栏样式
.headerNavigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;

  .leftSection {
    display: flex;
    align-items: center;
    flex: 1;

    .backButton {
      width: 24px;
      height: 24px;
      margin-right: 16px;
      cursor: pointer;
    }

    .title {
      font-size: 18px;
      color: var(--text-color);
      font-weight: 500;
      margin: 0;
    }
  }

  .rightSection {
    .createButton {
      width: 24px;
      height: 24px;
      cursor: pointer;
    }
  }
}

.breadcrumb {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: var(--background-color);
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 8px;

  &::-webkit-scrollbar {
    display: none;
  }

  .breadcrumbItem {
    color: rgba(255, 178, 29, 1);
    background-color: #fff4dd;
    padding: 5px 10px;
    border-radius: 10px;
    font-size: 14px;
    cursor: pointer;
    flex-shrink: 0;

    &.active {
      background-color: rgba(255, 178, 29, 0.2);
      color: rgba(255, 178, 29, 1);
      font-weight: 500;
    }

    &:hover {
      opacity: 0.8;
    }
  }

  .breadcrumbSeparator {
    margin: 0 4px;
    color: var(--subtitle-text-color);
    font-size: 12px;
    flex-shrink: 0;
  }
}

.scrollableContent {
  flex: 1;
  overflow-y: auto;
  // height:100%;
  padding-bottom: 140px; // 为底部按钮留出足够空间（两个按钮 + 间距 + padding）
}

// 加载状态样式
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 16px;
  color: var(--subtitle-text-color);

  span {
    margin-top: 12px;
    font-size: 14px;
  }
}

.fileList {
  padding: 0 16px;

  .fileItem {
    display: flex;
    align-items: center;
    padding: 12px 0;
    cursor: pointer;
    border-radius: 8px;
    margin-bottom: 4px;
    transition: background-color 0.2s ease;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.02);
    }

    &.selected {
      background-color: rgba(50, 186, 192, 0.1);
      border: 1px solid rgba(50, 186, 192, 0.3);
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }

    .fileIcon {
      width: 40px;
      height: 40px;
      margin-right: 12px;
      margin-left: 8px;
      display: flex;
      align-items: center;
      justify-content: center;

      .folderIcon {
        width: 100%;
        height: 100%;
        background-color: #ffc14f;
        border-radius: 4px;
        mask-size: cover;
        -webkit-mask-size: cover;
        mask-repeat: no-repeat;
        -webkit-mask-repeat: no-repeat;
        mask-position: center;
        -webkit-mask-position: center;
      }
    }

    .fileInfo {
      flex: 1;
      overflow: hidden;
      padding-right: 8px;

      .fileName {
        display: flex;
        align-items: center;
        font-size: 16px;
        color: var(--text-color);
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 400;
      }

      .fileDetails {
        font-size: 12px;
        color: var(--subtitle-text-color);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .emptyState {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 16px;
    color: var(--subtitle-text-color);
    font-size: 14px;
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background-color: var(--background-color);
  border-top: 1px solid var(--border-color);
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 12px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

  .newFolderButton {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    cursor: pointer;
    padding: 12px 16px;
    background: transparent;
    border-radius: 12px;
    border: 1px solid var(--thinLine-background-color);
    width: 100%;
    min-height: 48px;

    &:hover {
      background: rgba(0, 0, 0, 0.02);
    }

    &:active {
      background: rgba(0, 0, 0, 0.05);
    }

    .folderIconContainer {
      width: 20px;
      height: 20px;
      margin-right: 12px;
      flex-shrink: 0;

      .folderIcon {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .newFolderText {
      font-size: 16px;
      color: var(--text-color);
      font-weight: 400;
    }
  }

  .confirmButton {
    width: 100%;
    height: 48px;
    border-radius: 12px;
    background-color: var(--primary-color);
    border: none;
    font-size: 16px;
    font-weight: 500;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    &:active {
      background-color: var(--primary-color);
    }

    &:disabled {
      background-color: var(--primary-color);
      opacity: 0.3;
      cursor: not-allowed;
    }
  }
}
