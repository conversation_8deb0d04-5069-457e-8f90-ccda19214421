.synchronizationContainer {
    padding: 20px;
    background-color: var(--background-color);
    //   min-height: 100v;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: var(--background-color);
        margin-bottom: 20px;
        padding: 16px 16px;

        .title {
            display: flex;
            align-items: center;
            gap: 8px;

            h2 {
                margin: 0;
                font-size: 16px;
                font-weight: 500;
                color: #979797;
            }

            .taskCount {
                font-size: 14px;
                color: #999;
                font-weight: 400;
            }
        }

        .addButton {
            height: 32px;
            padding: 0 16px;
            font-size: 14px;

            border: none;
            color: #979797;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;

            &.enabled {
                            background-color: var(--background-color);
                color: #61A3FF;

            }

            &.disabled {
                            background-color: var(--background-color);
                color: #979797;
            }
        }
    }

    .taskList {
        border-radius: 8px;
        overflow: hidden;
        // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .taskItem {
            background-color: var(--download-card-bg-color);
            margin-bottom: 10px;
            border-radius: 10px;

            &:last-child {
                border-bottom: none;
            }

            .taskRow {
                display: flex;
                align-items: center;

                padding: 16px 20px;
                gap: 16px;
                min-height: 80px;

                .taskLeft {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    flex: 0 0 200px;

                    .folderIcon {
                        width: 40px;
                        height: 40px;
                        border-radius: 6px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        font-size: 18px;
                    }

                    .taskInfo {
                        .taskName {
                            font-size: 14px;
                            font-weight: 500;
                            color: var(--text-color);
                            margin-bottom: 4px;
                            max-width: 140px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }

                        .taskPath {
                            font-size: 12px;
                            color: #999;
                            max-width: 140px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                    }
                }

                .taskCenter {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 8px;



                    .statusContainer {
                        display: flex;
                        align-items: center;


                    }

                    .progressContainer {
                        .progressBar {
                            margin-bottom: 4px;

                            :global(.ant-progress-inner) {
                                background-color: var(--download-card-bg-color);
                                // height: 6px;
                            }

                            :global(.ant-progress-bg) {
                                background-color: var(--primary-color);
                            }

                            // 失败状态的进度条样式
                            &.failed {
                                :global(.ant-progress-bg) {
                                    background-color: #ff4d4f !important;
                                }
                            }
                        }

                        .progressInfo {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;

                            .statusText {
                                font-size: 15px;
                                padding: 2px 8px;
                                border-radius: 4px;
                                font-weight: 400;
                                color: var(--text-color);
                            }

                            .sizeInfo {
                                font-size: 12px;
                                color: #666;
                                font-weight: 500;
                            }

                            // 根据状态显示不同颜色
                            .statusWaiting {
                                color: #1890ff !important;
                            }

                            .statusSyncing {
                                color: #666 !important;
                            }

                            .statusCompleted {
                                color: #52c41a !important;
                            }

                            .statusPaused {
                                color: #fa8c16 !important;
                            }

                            .statusFailed {
                                color: #ff4d4f !important;
                            }

                            .fileCount {
                                font-size: 12px;
                                color: #666;
                            }

                            .updateTime {
                                font-size: 12px;
                                color: #999;
                            }
                        }

                        .taskPathContainer {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;

                            .taskPath {
                                font-size: 12px;
                                color: #999;
                            }

                            .updateTime {
                                font-size: 12px;
                                color: #999;
                            }
                        }
                    }
                }

                .taskRight {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    flex: 0 0 120px;
                    justify-content: flex-end;

                    .fileCount {
                        font-size: 14px;
                        color: #666;
                        min-width: 30px;
                        text-align: right;
                    }

                    .actionButtons {
                        display: flex;
                        gap: 4px;

                        .actionButton {
                            width: 24px;
                            height: 24px;
                            padding: 0;
                            border: none;
                            background: none;
                            color: #1890ff;
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            &:hover {
                                background-color: #e6f7ff;
                                color: #1890ff;
                            }
                        }

                        .deleteButton {
                            width: 24px;
                            height: 24px;
                            padding: 0;
                            border: none;
                            background: none;
                            color: #666;
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            &:hover {
                                background-color: #fff2f0;
                                color: #ff4d4f;
                            }
                        }
                    }
                }
            }
        }
    }
}

// 删除确认对话框内容样式
.deleteConfirmContent {
    padding: 16px 0;
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    text-align: center;
}