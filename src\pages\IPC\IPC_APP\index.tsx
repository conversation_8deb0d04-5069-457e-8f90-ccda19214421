import { Route, useLocation } from "react-router-dom";
import { useRequest } from "ahooks";
import IPCHome from "./Home";
import NotAdded from "./NotAdded";
import styles from "./index.module.scss";
import { useState, useEffect } from "react";
import { listRecordCamera, CameraInfo } from "@/api/ipc";
import { Toast } from "antd-mobile";

export default function CameraManagement(props: {
  children?: React.ReactNode;
}) {
  const [hasCameras, setHasCameras] = useState(false);
  const [cameraList, setCameraList] = useState<CameraInfo[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  // const { path } = useRouteMatch();
  const location = useLocation<{ shouldRefresh?: boolean }>();

  const { run: fetchCameraList } = useRequest(
    () => listRecordCamera({ did: [] }),
    {
      manual: true, // 改为手动执行，避免自动调用
      onSuccess: (res) => {
        if(res && (res.code === 5000 || res.code === 1700)){
          Toast.show(res?.result);
          return;
        }
        const cameras = res.data?.camera || [];
        setCameraList(cameras);
        setHasCameras(cameras.length > 0);
        setIsInitialized(true);
      },
      onError: (err) => {
        console.error("摄像头列表获取失败:", err);
        setCameraList([]);
        setHasCameras(false);
        setIsInitialized(true);
      },
    }
  );

  // 提供刷新摄像头列表的方法
  const refreshCameraList = () => {
    fetchCameraList();
  };

  // 组件挂载时获取数据
  useEffect(() => {
    if (!isInitialized) {
      fetchCameraList();
    }
  }, [isInitialized, fetchCameraList]);

  // 监听路由状态变化，如果需要刷新则重新获取数据
  useEffect(() => {
    if (location.state?.shouldRefresh && isInitialized) {
      fetchCameraList();
    }
  }, [location.state?.shouldRefresh, isInitialized, fetchCameraList]);

  // if (loading)
  //   return (
  //     <div className={styles.loadingContainer}>
  //       <Loading color="primary" />
  //       <span className={styles.loadingText}>加载中...</span>
  //     </div>
  //   );

  return (
    <div id="cameraManagementContainer" className={styles.cameraManagementContainer}>
      <div className={styles.top}></div>
      <div className={styles.content}>
        {props.children}
        <Route exact path="/cameraManagement_app">
          {hasCameras ? (
            <IPCHome
              cameraList={cameraList}
              refreshCameraList={refreshCameraList}
            />
          ) : (
            <NotAdded />
          )}
        </Route>
      </div>
    </div>
  );
}
